import { Base<PERSON>dapter, FindManyArgs, OperationContext } from "@unilab/urpc-core";
import { CameraDatasetEntity } from "../entities/physicial";

export class CameraDatasetAdapter extends BaseAdapter<CameraDatasetEntity> {
  async findMany(
    args?: FindManyArgs<CameraDatasetEntity> | undefined,
    ctx?: OperationContext
  ): Promise<CameraDatasetEntity[]> {
    const url = ctx?.url;
    console.log("Context URL:", url);

    const limit = args?.limit || 10;
    const offset = args?.offset || 0;

    // 创建模拟数据
    const mockData: CameraDatasetEntity[] = [];
    for (let i = 0; i < limit; i++) {
      const entity = new CameraDatasetEntity();
      entity.id = `Camera_${String(535 + i + offset).padStart(4, '0')}`;
      entity.type = "camera";
      // 保持默认的矩阵值和属性
      mockData.push(entity);
    }

    return Promise.resolve(mockData);
  }
}
