import { CameraDatasetEntity } from "./entities/physicial";
import { repo, URPC } from "@unilab/urpc";

URPC.init({
  baseUrl: "https://p01--qsdata-server--p82yxqzl6dpk.code.run/",
  timeout: 30000, // Match server timeout
});

const fetchPhysical = async () => {
  console.log("🧪 Testing Camera Dataset Entity (DuckDB source)...");
  try {
    const data = await repo<CameraDatasetEntity>({
      entity: "CameraDatasetEntity",
      source: "duckdb",
      context: {
        url: "https://cdn-lfs-us-1.hf.co/repos/18/40/18404ac7ac0e354cf643fd38fc115bdf91c53588cacf6d6c8ced92e1b12089bd/9be51c801fe3f7a32c6ddab3eafaa16ec0bddf0e6d0855edd4a780fa31915abe?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27calibration_2025_format.json%3B+filename%3D%22calibration_2025_format.json%22%3B&response-content-type=application%2Fjson&Expires=1754381899&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1NDM4MTg5OX19LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zLzE4LzQwLzE4NDA0YWM3YWMwZTM1NGNmNjQzZmQzOGZjMTE1YmRmOTFjNTM1ODhjYWNmNmQ2YzhjZWQ5MmUxYjEyMDg5YmQvOWJlNTFjODAxZmUzZjdhMzJjNmRkYWIzZWFmYWExNmVjMGJkZGYwZTZkMDg1NWVkZDRhNzgwZmEzMTkxNWFiZT9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSomcmVzcG9uc2UtY29udGVudC10eXBlPSoifV19&Signature=bXmvqmz9AvdLWk3UEJMWlL4SCv2oepTJ0attz-y51%7EH0faDtPatU2QJyc0pYR-zMCS7HZbD8EcGD%7E6SmEE28ABv8msq3sXqBzKsCtbMYxm0fN-da%7E9mehlC2%7EP3ruALCdszOClw2nuX4%7EAhweROjDfk8FDodtuKnpa08%7EdkDqF6ToAzC9KemAChd74X9cDs1lGXDwM3M-hY-3Ce5qRsS47EAah38eQnAe4CtPp8r0uDAWo6fR1J5rkXjAdeYF%7Ezoc8wbGefuvnEd53LbEWN2xSQ2-6E0VlMYirn6s%7EjqPnhlXEgbxf4d5m-CaXkAiNlQWFdlC9XbIw0xa9GulQYCug__&Key-Pair-Id=K24J24Z295AEI9"
      }
    }).findMany({
      limit: 5,
      offset: 0,
    });
    console.log("[1] Camera Dataset Entity =>", JSON.stringify(data, null, 2));

    if (data.length > 0) {
      console.log("🔍 Testing specific camera lookup...");
      const specificCamera = await repo<CameraDatasetEntity>({
        entity: "CameraDatasetEntity",
        source: "duckdb",
        context: {
          url: "https://cdn-lfs-us-1.hf.co/repos/18/40/18404ac7ac0e354cf643fd38fc115bdf91c53588cacf6d6c8ced92e1b12089bd/9be51c801fe3f7a32c6ddab3eafaa16ec0bddf0e6d0855edd4a780fa31915abe?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27calibration_2025_format.json%3B+filename%3D%22calibration_2025_format.json%22%3B&response-content-type=application%2Fjson&Expires=1754381899&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1NDM4MTg5OX19LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zLzE4LzQwLzE4NDA0YWM3YWMwZTM1NGNmNjQzZmQzOGZjMTE1YmRmOTFjNTM1ODhjYWNmNmQ2YzhjZWQ5MmUxYjEyMDg5YmQvOWJlNTFjODAxZmUzZjdhMzJjNmRkYWIzZWFmYWExNmVjMGJkZGYwZTZkMDg1NWVkZDRhNzgwZmEzMTkxNWFiZT9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSomcmVzcG9uc2UtY29udGVudC10eXBlPSoifV19&Signature=bXmvqmz9AvdLWk3UEJMWlL4SCv2oepTJ0attz-y51%7EH0faDtPatU2QJyc0pYR-zMCS7HZbD8EcGD%7E6SmEE28ABv8msq3sXqBzKsCtbMYxm0fN-da%7E9mehlC2%7EP3ruALCdszOClw2nuX4%7EAhweROjDfk8FDodtuKnpa08%7EdkDqF6ToAzC9KemAChd74X9cDs1lGXDwM3M-hY-3Ce5qRsS47EAah38eQnAe4CtPp8r0uDAWo6fR1J5rkXjAdeYF%7Ezoc8wbGefuvnEd53LbEWN2xSQ2-6E0VlMYirn6s%7EjqPnhlXEgbxf4d5m-CaXkAiNlQWFdlC9XbIw0xa9GulQYCug__&Key-Pair-Id=K24J24Z295AEI9"
        }
      }).findMany({
        where: {
          id: data[0].id,
        },
        limit: 1,
      });
      console.log("[2] Specific Camera =>", JSON.stringify(specificCamera, null, 2));
    }
  } catch (error) {
    console.error("❌ Error fetching Camera Dataset Entity:", error);
  }
};


// Run all tests
const runAllTests = async () => {
  console.log("🚀 Starting Camera Dataset Entity client tests...\n");

  await fetchPhysical();
  console.log("\n" + "=".repeat(50) + "\n");

  console.log("\n✅ All tests completed!");
};

runAllTests();
