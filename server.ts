import { URPC } from "@unilab/urpc-hono";
import { Plugin } from "@unilab/urpc-core";
import { CameraDatasetEntity } from "./entities/physicial";
import { CameraDatasetAdapter } from "./adapters/physicial";
import { GeocodingEntity, GeocodingQueryResult } from "./entities/geocoding";
import { GeocodingAdapter } from "./adapters/open-meteo-geocoding";
import { Current, CurrentUnits, Hourly, HourlyUnits, WeatherEntity, WeatherQueryResult } from "./entities/weather";
import { WeatherAdapter } from "./adapters/open-meteo-weather";

export const GeocodingPlugin: Plugin = {
  entities: [GeocodingEntity, GeocodingQueryResult],
  adapters: [
    {
      source: "open-meteo",
      entity: "GeocodingEntity",
      adapter: new GeocodingAdapter(),
    },
  ],
};

export const WeatherPlugin: Plugin = {
  entities: [
    WeatherEntity,
    WeatherQueryResult,
    Current,
    CurrentUnits,
    Hourly,
    HourlyUnits,
  ],
  adapters: [
    {
      source: "open-meteo",
      entity: "WeatherEntity",
      adapter: new WeatherAdapter(),
    },
  ],
};

const MyPlugin: Plugin = {
  entities: [CameraDatasetEntity],
  adapters: [
    {
      source: 'duckdb',
      entity: 'CameraDatasetEntity',
      adapter: new CameraDatasetAdapter(),
    }
  ]
};

const app = URPC.init({
  plugins: [MyPlugin, GeocodingPlugin, WeatherPlugin],
  middlewares: [

  ],
  entityConfigs: {},
});

const server = {
  port: 3000,
  idleTimeout: 180,
  fetch: app.fetch,
};

console.log(`🚀 Server running on http://localhost:${server.port}`);

export default server;
